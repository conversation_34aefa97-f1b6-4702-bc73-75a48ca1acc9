//
//  ReplyModel.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/3/25.
//

import Foundation

struct ReplyModel {
    let avatar: String
    let username: String
    let content: String
    let time: String
    let replyToUser: String? // 被回复人昵称
    let replyToContent: String? // 被回复内容
    let isLiked: Bool // 是否点赞
    let mentionRanges: [MentionRange] // @用户的范围信息

    // 为了兼容现有代码，提供不包含mentionRanges的初始化方法
    init(avatar: String, username: String, content: String, time: String, replyToUser: String? = nil, replyToContent: String? = nil, isLiked: Bool, mentionRanges: [MentionRange] = []) {
        self.avatar = avatar
        self.username = username
        self.content = content
        self.time = time
        self.replyToUser = replyToUser
        self.replyToContent = replyToContent
        self.isLiked = isLiked
        self.mentionRanges = mentionRanges
    }
}
