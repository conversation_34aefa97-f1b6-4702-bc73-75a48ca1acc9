//
//  UserInfoEditNameViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/15.
//

import Foundation
import UIKit

class UserInfoEditNameViewController: BaseViewController {
    
    // MARK: - 属性
    
    var currentName: String = ""
    var maxLength: Int = 24
    var minLength: Int = 2
    var onNameUpdated: ((String) -> Void)?
    
    // MARK: - UI组件
    
    private lazy var nameTextField: UITextField = {
        let textField = UITextField()
        textField.font = UIFont.systemFont(ofSize: 14)
        textField.textColor = UIColor(hex: "#333333")
        textField.returnKeyType = .done
        textField.clearButtonMode = .whileEditing
        textField.placeholder = "请输入名字"
        textField.delegate = self
        textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        return textField
    }()
    
    private lazy var textFieldContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 5
        return view
    }()
    
    private lazy var countLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.textAlignment = .right
        return label
    }()
    
    private lazy var infoLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#777777")
        label.text = "请设置\(minLength)-\(maxLength)个字符，不包括@＜＞/等无效字符，7天内仅可修改1次名字"
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configureBaseSettings()
        setupUI()
        configureInitialData()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        nameTextField.becomeFirstResponder()
    }
    
    // MARK: - 设置方法
    
    private func configureBaseSettings() {
        // 设置导航栏标题
        navTitle = "编辑名字"
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 显示返回按钮
        showBackButton = true
        
        // 设置背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加确认按钮
        rightNavTitle = "确认"
        rightNavAction = #selector(confirmButtonTapped)
        rightNavButtonTintColor = UIColor(hex: "#FF6236")
    }
    
    private func setupUI() {
        // 添加输入框容器
        contentView.addSubview(textFieldContainer)
        textFieldContainer.snp.makeConstraints { make in
//            make.top.left.right.equalToSuperview()
            make.top.equalToSuperview().offset(25)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(40)
        }
        
        // 首先添加字数统计标签到容器中
        textFieldContainer.addSubview(countLabel)
        countLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(50)
        }
        
        // 然后添加输入框到容器中
        textFieldContainer.addSubview(nameTextField)
        nameTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.right.equalTo(countLabel.snp.left).offset(-8)
            make.height.equalTo(40)
        }
        
        // 添加提示信息
        contentView.addSubview(infoLabel)
        infoLabel.snp.makeConstraints { make in
            make.top.equalTo(textFieldContainer.snp.bottom).offset(10)
            make.left.equalToSuperview().offset(32)
            make.right.equalToSuperview().offset(-32)
        }
    }
    
    private func configureInitialData() {
        nameTextField.text = currentName
        updateCountLabel()
        validateInput()
    }
    
    // MARK: - 事件处理
    
    @objc private func textFieldDidChange() {
        updateCountLabel()
        validateInput()
    }
    
    @objc private func confirmButtonTapped() {
        guard let name = nameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines), !name.isEmpty else {
            InfoPopupView.show(in: view, message: "请输入名字")
            return
        }

        // 检查名字是否有变化
        if !hasChanged(name) {
            InfoPopupView.show(in: view, message: "名字没有变化")
            return
        }

        // 检查名字是否有效
        if let errorMessage = getNameValidationError(name) {
            InfoPopupView.show(in: view, message: errorMessage)
            return
        }

        // 回调更新的名字
        onNameUpdated?(name)

        // 返回上一页
        navigationController?.popViewController(animated: true)
    }
    
    // MARK: - 辅助方法
    
    private func updateCountLabel() {
        let currentCount = nameTextField.text?.count ?? 0
        countLabel.text = "\(currentCount)/\(maxLength)"
    }
    
    private func validateInput() {
        guard let text = nameTextField.text else {
            rightNavButton?.isEnabled = false
            updateButtonAppearance(isEnabled: false)
            return
        }
        
        // 检查输入是否有效且与原名字不同
        let isValid = isValidName(text) && hasChanged(text)
        updateButtonAppearance(isEnabled: isValid)
    }
    
    private func updateButtonAppearance(isEnabled: Bool) {
        // 设置按钮状态
        rightNavButton?.isEnabled = isEnabled
        
        // 设置按钮颜色 - 无论是否可点击，颜色都是#FF6236
        // 这里可以通过不同的alpha值或其他视觉效果来区分状态
        rightNavButton?.setTitleColor(UIColor(hex: "#FF6236"), for: .normal)
        
        // 可以通过alpha值来视觉上区分是否可点击
        rightNavButton?.alpha = isEnabled ? 1.0 : 0.5
    }
    
    private func isValidName(_ name: String) -> Bool {
        // 检查长度
        guard name.count >= minLength && name.count <= maxLength else {
            return false
        }

        // 检查是否包含无效字符（全部英文符号，包括下划线）
        let invalidCharacters = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "-", "_", "=", "+", "[", "]", "{", "}", "\\", "|", ";", ":", "'", "\"", ",", ".", "<", ">", "/", "?", "`", "~"]
        for char in invalidCharacters {
            if name.contains(char) {
                return false
            }
        }

        return true
    }

    /// 获取名字验证失败的具体原因
    private func getNameValidationError(_ name: String) -> String? {
        // 检查长度
        if name.count < minLength {
            return "名字至少需要\(minLength)个字符"
        }
        if name.count > maxLength {
            return "名字不能超过\(maxLength)个字符"
        }

        // 检查是否包含无效字符（全部英文符号，包括下划线）
        let invalidCharacters = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "-", "_", "=", "+", "[", "]", "{", "}", "\\", "|", ";", ":", "'", "\"", ",", ".", "<", ">", "/", "?", "`", "~"]
        for char in invalidCharacters {
            if name.contains(char) {
                return "名字不能包含无效字符：\(char)"
            }
        }

        return nil
    }
    
    private func hasChanged(_ name: String) -> Bool {
        // 检查输入的名字是否与原始名字不同
        return name != currentName
    }
}

// MARK: - UITextFieldDelegate

extension UserInfoEditNameViewController: UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        // 限制最大输入长度
        guard let text = textField.text else { return true }
        let newLength = text.count + string.count - range.length
        if newLength > maxLength {
            return false
        }

        // 检查输入字符是否包含无效字符（全部英文符号，包括下划线）
        if !string.isEmpty {
            let invalidCharacters = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "-", "_", "=", "+", "[", "]", "{", "}", "\\", "|", ";", ":", "'", "\"", ",", ".", "<", ">", "/", "?", "`", "~"]
            for char in invalidCharacters {
                if string.contains(char) {
                    return false
                }
            }
        }

        return true
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        if isValidName(textField.text ?? "") && hasChanged(textField.text ?? "") {
            confirmButtonTapped()
        }
        return true
    }
}
