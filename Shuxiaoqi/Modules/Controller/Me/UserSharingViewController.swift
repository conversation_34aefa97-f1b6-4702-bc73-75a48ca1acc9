//
//  UserSharingViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/4/19.
//
//  用户分享

import UIKit
import SnapKit

class UserSharingViewController: BaseViewController {
    
    // MARK: - 公共属性
    private let userId: String // 传入的用户ID（customerId）
    
    // MARK: - 属性
    // 用户分享信息
    private var userName: String = ""            // 用户昵称
    private var serverUserId: String = ""        // customerId，用于二维码和后端定位
    private var treeNumber: String = ""          // 树小柒号 (customerAccount)，用于展示/搜索
    private var userAvatar: UIImage? = UIImage(named: "default_avatar") // 默认头像
    
    // UI组件
    private lazy var qrCodeImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = .white // 背景设为白色，确保二维码清晰
//        imageView.layer.cornerRadius = 87 // 移除圆角，使其为方形
        imageView.clipsToBounds = true
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = userName
        label.textColor = .black
        label.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var idContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var idPrefixLabel: UILabel = {
        let label = UILabel()
        label.text = "树小柒号："
        label.textColor = .darkGray
        label.font = UIFont.systemFont(ofSize: 15)
        return label
    }()
    
    private lazy var idLabel: UILabel = {
        let label = UILabel()
        label.text = treeNumber
        label.textColor = .darkGray
        label.font = UIFont.systemFont(ofSize: 15)
        return label
    }()
    
    private lazy var copyButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "sharing_copy") ?? UIImage(systemName: "doc.on.doc"), for: .normal)
        button.tintColor = .darkGray
        button.addTarget(self, action: #selector(copyIDButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var optionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fill // 修改 distribution
        return stackView
    }()
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "账号分享"
        setupUI()
        fetchPersonalHomeInfo()
    }
    
    // MARK: - 自定义初始化
    init(userId: String) {
        self.userId = userId
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        view.backgroundColor = .white
        contentView.backgroundColor = .white
        // 添加二维码视图和装饰
//        view.addSubview(decorationView)
        view.addSubview(qrCodeImageView)
//        qrCodeImageView.addSubview(avatarImageView)
        
        // 添加名称标签
        view.addSubview(nameLabel)
        
        // 添加ID容器视图
        view.addSubview(idContainerView)
        idContainerView.addSubview(idPrefixLabel)
        idContainerView.addSubview(idLabel)
        idContainerView.addSubview(copyButton)
        
        // 添加选项堆栈视图
        view.addSubview(optionsStackView)
        
        // 添加选项行
        let scanOption = createOptionView(
            icon: "sharing_sys",
            title: "扫一扫",
            subtitle: "扫描树小柒码加好友",
            action: #selector(scanButtonTapped)
        )
        
        let wechatOption = createOptionView(
            icon: "sharing_wx",
            title: "添加微信好友",
            subtitle: "分享我的口令到微信添加好友",
            action: #selector(wechatButtonTapped)
        )
        
        let downloadOption = createOptionView(
            icon: "sharing_dw",
            title: "下载",
            subtitle: "保存二维码到相册",
            action: #selector(downloadButtonTapped)
        )
        
        optionsStackView.addArrangedSubview(scanOption)
        optionsStackView.addArrangedSubview(addSeparator())
        optionsStackView.addArrangedSubview(wechatOption)
        optionsStackView.addArrangedSubview(addSeparator())
        optionsStackView.addArrangedSubview(downloadOption)
        optionsStackView.addArrangedSubview(addSeparator())
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupConstraints() {
        // 二维码视图约束
//        decorationView.snp.makeConstraints { make in
//            make.center.equalTo(qrCodeImageView)
//            make.width.height.equalTo(260)
//        }
//
        qrCodeImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(navBar.snp.bottom).offset(35)
            make.width.height.equalTo(174)
        }
        
//        avatarImageView.snp.makeConstraints { make in
//            make.center.equalToSuperview()
//            make.width.height.equalTo(70)
//        }
//
        
        // 名称标签约束
        nameLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(qrCodeImageView.snp.bottom).offset(16)
            make.height.equalTo(26)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // ID容器约束
        idContainerView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
            make.height.equalTo(30)
        }
        
        idPrefixLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.height.equalToSuperview()
        }
        
        idLabel.snp.makeConstraints { make in
            make.leading.equalTo(idPrefixLabel.snp.trailing)
            make.centerY.equalToSuperview()
            make.height.equalToSuperview()
        }
        
        copyButton.snp.makeConstraints { make in
            make.leading.equalTo(idLabel.snp.trailing).offset(8)
            make.trailing.centerY.equalToSuperview()
            make.size.equalTo(24)
        }
                                                                                  
        // 选项堆栈视图约束
        optionsStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(idContainerView.snp.bottom).offset(40)
            make.bottom.lessThanOrEqualTo(view.safeAreaLayoutGuide.snp.bottom).offset(-20) // 增加底部间距
        }
    }
    
    // 创建选项视图
    private func createOptionView(icon: String, title: String, subtitle: String, action: Selector) -> UIView {
        let container = UIView()
        container.backgroundColor = .white
        
        // 图标
        let iconView = UIImageView()
        if let systemImage = UIImage(systemName: icon) {
            iconView.image = systemImage
            iconView.tintColor = UIColor.systemBlue
        } else {
            iconView.image = UIImage(named: icon)
        }
        iconView.contentMode = .scaleAspectFit
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = .black
        titleLabel.font = UIFont.systemFont(ofSize: 16)
        
        // 副标题
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.textColor = .darkGray
        subtitleLabel.font = UIFont.systemFont(ofSize: 12)
        
        // 箭头
        let arrowView = UIImageView(image: UIImage(named: "sharing_right_arrow"))
        arrowView.contentMode = .scaleAspectFit
        arrowView.tintColor = .lightGray
        
        // 添加到容器
        container.addSubview(iconView)
        container.addSubview(titleLabel)
        container.addSubview(subtitleLabel)
        container.addSubview(arrowView)
        
        // 设置约束
        iconView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconView.snp.trailing).offset(15)
            make.bottom.equalTo(container.snp.centerY).offset(-2)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(container.snp.centerY).offset(2)
        }
        
        arrowView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.size.equalTo(20)
        }
        
        // 设置容器高度
        container.snp.makeConstraints { make in
            make.height.equalTo(60)
        }
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        container.addGestureRecognizer(tapGesture)
        container.isUserInteractionEnabled = true
        
        return container
    }
    
    // 添加分隔线
    private func addSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        separator.snp.makeConstraints { make in
            make.height.equalTo(0.5)
        }
        return separator
    }
    
    // 生成带头像的二维码
    private func generateQRCodeWithAvatar() {
        // 构建二维码数据: {"content":"<customerId>", "type":1}
        let qrDict: [String: Any] = [
            "content": serverUserId,
            "type": 1
        ]
        guard let jsonData = try? JSONSerialization.data(withJSONObject: qrDict, options: []),
              let qrData = String(data: jsonData, encoding: .utf8) else {
            print("生成二维码 JSON 字符串失败")
            return
        }
        guard let qrFilter = CIFilter(name: "CIQRCodeGenerator") else { return }
        
        qrFilter.setValue(qrData.data(using: .utf8), forKey: "inputMessage")
        qrFilter.setValue("H", forKey: "inputCorrectionLevel") // 高容错率
        
        guard let ciImage = qrFilter.outputImage else { return }
        
        // 放大 CIImage
        let transform = CGAffineTransform(scaleX: 10, y: 10)
        let scaledCIImage = ciImage.transformed(by: transform)
        
        // 将 CIImage 转换为 CGImage
        let context = CIContext()
        guard let cgImage = context.createCGImage(scaledCIImage, from: scaledCIImage.extent) else { return }
        let qrCodeImage = UIImage(cgImage: cgImage)
        
        // 准备头像
        guard let avatar = userAvatar else {
            qrCodeImageView.image = qrCodeImage
            return
        }
        
        // 开始绘制带头像的二维码
        UIGraphicsBeginImageContextWithOptions(qrCodeImage.size, false, qrCodeImage.scale)
        qrCodeImage.draw(in: CGRect(origin: .zero, size: qrCodeImage.size))
        
        // 计算头像绘制区域（二维码中心约1/4区域）
        let avatarSize = CGSize(width: qrCodeImage.size.width * 0.25, height: qrCodeImage.size.height * 0.25)
        let avatarX = (qrCodeImage.size.width - avatarSize.width) / 2
        let avatarY = (qrCodeImage.size.height - avatarSize.height) / 2
        let avatarRect = CGRect(x: avatarX, y: avatarY, width: avatarSize.width, height: avatarSize.height)
        
        // 绘制白色背景（可选，增加对比度）
        let backgroundRect = avatarRect.insetBy(dx: -4, dy: -4) // 白色边框比头像稍大
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        UIColor.white.setFill()
        backgroundPath.fill()
        
        // 绘制头像
        let avatarPath = UIBezierPath(roundedRect: avatarRect, cornerRadius: 8)
        avatarPath.addClip() // 裁剪绘制区域为圆角矩形
        avatar.draw(in: avatarRect)
        
        // 获取最终图像
        let finalImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        qrCodeImageView.image = finalImage ?? qrCodeImage // 如果绘制失败，显示原始二维码
    }
    
    // MARK: - 数据请求
    /// 获取个人主页信息并更新分享页
    private func fetchPersonalHomeInfo() {
        APIManager.shared.getPersonHomeInfo(customerId: userId) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let info):
                    if let data = info.data {
                        self.userName = data.displayNickName
                        // 保存 ID
                        self.serverUserId = data.customerId
                        self.treeNumber = data.customerAccount.isEmpty ? data.customerId : data.customerAccount

                        // 更新 UI
                        self.nameLabel.text = self.userName
                        self.idLabel.text = self.treeNumber

                        // 加载头像
                        if let url = URL(string: data.wxAvator), !data.wxAvator.isEmpty {
                            self.loadAvatar(from: url)
                        } else {
                            self.generateQRCodeWithAvatar() // 无头像 URL 直接生成二维码
                        }
                    }
                case .failure(let error):
                    print("获取个人信息失败: \(error.localizedDescription)")
                    // 即使失败也至少展示默认二维码
                    self.generateQRCodeWithAvatar()
                }
            }
        }
    }

    /// 异步加载头像并生成携带头像的二维码
    private func loadAvatar(from url: URL) {
        DispatchQueue.global().async {
            if let data = try? Data(contentsOf: url), let image = UIImage(data: data) {
                DispatchQueue.main.async {
                    self.userAvatar = image
                    self.generateQRCodeWithAvatar()
                }
            } else {
                DispatchQueue.main.async {
                    self.generateQRCodeWithAvatar() // 加载失败也生成无头像二维码
                }
            }
        }
    }
    
    // MARK: - 按钮事件
    
    @objc private func copyIDButtonTapped() {
        UIPasteboard.general.string = treeNumber.isEmpty ? serverUserId : treeNumber
        showToast("已复制账号，快去分享给好友吧")
    }
    
    @objc private func scanButtonTapped() {
        // 打开扫描二维码页面
        print("扫一扫按钮点击")
        let scanVC = QRScanViewController()
        self.navigationController?.pushViewController(scanVC, animated: true)
    }
    
    @objc private func wechatButtonTapped() {
        print("=== 微信分享按钮点击 ===")
        print("用户名: \(userName)")
        print("树小柒号: \(treeNumber)")
        print("二维码图片是否存在: \(qrCodeImageView.image != nil)")

        // 检查必要数据
        guard !treeNumber.isEmpty else {
            print("❌ 树小柒号为空，无法分享")
            showToast("数据加载中，请稍后再试")
            return
        }

        guard let qrImage = qrCodeImageView.image else {
            print("❌ 二维码图片不存在")
            showToast("二维码生成中，请稍后再试")
            return
        }

        // 检查微信是否可用
        guard WXApi.isWXAppInstalled() else {
            print("❌ 微信未安装")
            showToast("请先安装微信")
            return
        }

        guard WXApi.isWXAppSupport() else {
            print("❌ 微信版本不支持")
            showToast("微信版本过低，请升级微信")
            return
        }

        // 构建分享口令
        let commandString = "复制到【树小柒】APP添加\(self.treeNumber)"
        print("分享口令: \(commandString)")

        let payload = SharePayload(
            title: "树小柒 · 我的好友码",
            description: commandString,
            link: nil,
            thumbnail: nil,
            image: qrImage,
            type: .invite(code: treeNumber),
            extras: ["command": commandString]
        )

        print("发送分享通知...")
        NotificationCenter.default.post(name: .shareRequested, object: payload)
        print("分享通知已发送")
    }
    
    @objc private func downloadButtonTapped() {
        // 下载二维码
        print("下载按钮点击")
        
        // 获取当前二维码图像
        guard let imageToSave = qrCodeImageView.image else {
            showToast("无法获取二维码图像")
            return
        }
        
        // 保存到相册
        UIImageWriteToSavedPhotosAlbum(imageToSave, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            showToast("保存失败: \(error.localizedDescription)")
        } else {
            showToast("二维码已保存到相册")
        }
    }
}
