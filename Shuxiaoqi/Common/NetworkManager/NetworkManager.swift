import Foundation
import Alamofire

/// 网络请求一级封装
class NetworkManager {
    
    /// 单例模式
    static let shared = NetworkManager()
    private init() {}
    
    /// HTTP 请求方法
    enum HTTPMethod: String {
        case get = "GET"
        case post = "POST"
        case put = "PUT"
        case delete = "DELETE"
    }
    
    /// 默认请求头
    private var defaultHeaders: HTTPHeaders {
        let headers: HTTPHeaders = [
            "Content-Type": "application/json",
            "Accept": "application/json"
        ]
        return headers
    }
    
    /// 通用请求方法
    /// - Parameters:
    ///   - url: 请求URL
    ///   - method: 请求方法
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - completion: 完成回调
    func request<T: Decodable>(
        url: String,
        method: HTTPMethod,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        completion: @escaping (Result<T, Error>) -> Void
    ) {
        let requestHeaders = headers ?? defaultHeaders
        let afMethod = Alamofire.HTTPMethod(rawValue: method.rawValue)
        
        AF.request(
            url,
            method: afMethod,
            parameters: parameters,
            encoding: JSONEncoding.default,
            headers: requestHeaders
        ).responseDecodable(of: T.self) { response in
            switch response.result {
            case .success(let value):
                completion(.success(value))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    /// GET 请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - completion: 完成回调
    func get<T: Decodable>(
        url: String,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        completion: @escaping (Result<T, Error>) -> Void
    ) {
        request(
            url: url,
            method: .get,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /// POST 请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - completion: 完成回调
    func post<T: Decodable>(
        url: String,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        completion: @escaping (Result<T, Error>) -> Void
    ) {
        request(
            url: url,
            method: .post,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /// PUT 请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - completion: 完成回调
    func put<T: Decodable>(
        url: String,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        completion: @escaping (Result<T, Error>) -> Void
    ) {
        request(
            url: url,
            method: .put,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /// DELETE 请求
    /// - Parameters:
    ///   - url: 请求URL
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - completion: 完成回调
    func delete<T: Decodable>(
        url: String,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        completion: @escaping (Result<T, Error>) -> Void
    ) {
        request(
            url: url,
            method: .delete,
            parameters: parameters,
            headers: headers,
            completion: completion
        )
    }
    
    /// 上传文件
    /// - Parameters:
    ///   - url: 请求URL
    ///   - fileData: 文件数据
    ///   - fileName: 文件名
    ///   - mimeType: 文件类型
    ///   - parameters: 请求参数
    ///   - headers: 请求头
    ///   - completion: 完成回调
    func uploadFile<T: Decodable>(
        url: String,
        fileData: Data,
        fileName: String,
        mimeType: String,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        completion: @escaping (Result<T, Error>) -> Void
    ) {
        let requestHeaders = headers ?? defaultHeaders
        
        AF.upload(
            multipartFormData: { multipartFormData in
                multipartFormData.append(
                    fileData,
                    withName: "file",
                    fileName: fileName,
                    mimeType: mimeType
                )
                
                if let parameters = parameters {
                    for (key, value) in parameters {
                        if let data = "\(value)".data(using: .utf8) {
                            multipartFormData.append(data, withName: key)
                        }
                    }
                }
            },
            to: url,
            method: .post,
            headers: requestHeaders
        ).responseDecodable(of: T.self) { response in
            switch response.result {
            case .success(let value):
                completion(.success(value))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
    
    /// 默认请求头
    func getDefaultHeaders() -> HTTPHeaders {
        var headers: HTTPHeaders = [
            "Content-Type": "application/json",
            "Accept": "*/*"
        ]
        
        // 添加token
        if let token = UserDefaults.standard.string(forKey: "userToken") {
            headers["Authorization"] = token
        }
        
        return headers
    }
    
    /// 记录请求日志
    func logRequest(url: String, method: String, headers: [String: String], parameters: [String: Any]?) {
        print("\n🚀 网络请求日志 🚀")
        print("📍 URL: \(url)")
        print("📝 方法: \(method)")
        print("📋 请求头: \(headers)")
        print("📦 参数: \(parameters ?? [:])")
        print("--------------------------------")
    }
    
    /// 记录响应日志
    func logResponse(url: String, statusCode: Int?, data: Data?, error: Error?) {
        print("\n📥 网络响应日志 📥")
        print("📍 URL: \(url)")
        print("📊 状态码: \(statusCode ?? -1)")
        
        if let data = data, let jsonString = String(data: data, encoding: .utf8) {
            print("📄 响应数据: \(jsonString)")
        } else if let data = data {
            print("📄 响应数据: [二进制数据：\(data.count) 字节]")
        }
        
        if let error = error {
            print("❌ 错误: \(error.localizedDescription)")
        }
        
        print("--------------------------------")
    }
    
    /// 网络请求方法
    func request(
        url: String,
        method: Alamofire.HTTPMethod,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        encoding: ParameterEncoding? = nil,
        completion: @escaping (Result<Data, Error>) -> Void
    ) {
        let requestHeaders = headers ?? getDefaultHeaders()

        // 确定编码方式
        let parameterEncoding: ParameterEncoding
        if let encoding = encoding {
            parameterEncoding = encoding
        } else {
            parameterEncoding = method == .get ? URLEncoding.default : JSONEncoding.default
        }

        // 记录请求
        let headerDict = Dictionary(uniqueKeysWithValues: requestHeaders.map { ($0.name, $0.value) })
        logRequest(url: url, method: method.rawValue, headers: headerDict, parameters: parameters)

        // 发送请求
        AF.request(
            url,
            method: method,
            parameters: parameters,
            encoding: parameterEncoding,
            headers: requestHeaders
        ).responseData { [weak self] response in
            // 记录响应
            self?.logResponse(
                url: url,
                statusCode: response.response?.statusCode,
                data: response.data,
                error: response.error
            )
            
            // 处理结果
            switch response.result {
            case .success(let data):
                completion(.success(data))
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
} 