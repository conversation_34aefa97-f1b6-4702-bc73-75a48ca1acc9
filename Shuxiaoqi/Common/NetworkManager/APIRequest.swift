/// API 请求方法
enum APIMethod: String {
    case get = "GET"
    case post = "POST"
    case put = "PUT"
    case delete = "DELETE"
}

/// API 请求编码类型
enum APIEncoding {
    case json
    case urlEncoded
}

/// API 请求配置
struct APIRequest {
    let path: String
    let method: APIMethod
    var parameters: [String: Any]?
    var headers: [String: String]?
    var baseURL: String?
    var encoding: APIEncoding?

    init(
        path: String,
        method: APIMethod = .post,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil,
        baseURL: String? = nil,
        encoding: APIEncoding? = nil
    ) {
        self.path = path
        self.method = method
        self.parameters = parameters
        self.headers = headers
        self.baseURL = baseURL
        self.encoding = encoding
    }
}