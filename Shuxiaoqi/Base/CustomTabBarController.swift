import UIKit
import ATAuthSDK
import Kingfisher
// 协调器文件在同一 target 内，无需单独 import

// 导入AuthManager
import Foundation

class CustomTabBarController: UITabBarController {
    
    // 自定义 TabBar
    let customTabBar = CustomTabBar()
    
    // 存储控制器数组
    private var customViewControllers: [UIViewController] = []
    
    // 当前选中的索引
    private var selectedTabIndex: Int = 0
    
    // MARK: - 新增: 首页双击检测
    /// 记录最后一次点击首页 Tab 的时间
    private var lastHomeTabTapDate: Date?
    
    // 标记是否处于登录成功状态，用于防止重复触发原生登录
    private var hasLoginSucceeded = false
    
    // 添加状态属性
    private var isLoggedIn: Bool = false // 默认为未登录
    private var favoriteItems: [Any] = [] // 模拟收藏列表数据，初始为空

    // 添加未登录提示视图
    private let loginPromptView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 透明背景
        view.isUserInteractionEnabled = true // 允许交互
        // 可以添加一个提示标签，例如
        let label = UILabel()
        label.text = "点击登录"
        label.textColor = UIColor(hex: "#333333")
        label.font = .boldSystemFont(ofSize: 18)
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        return view
    }()
    
    // 修复TabBar首次显示逻辑，防止每次viewDidAppear都强制showTabBar
    private var isFirstAppearance = true
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 隐藏系统TabBar
        tabBar.isHidden = true
        
        // 添加自定义TabBar
        view.addSubview(customTabBar)
        
        // 设置代理
        customTabBar.delegate = self
        
        // 设置TabBar的初始位置
        let height = 44 + view.safeAreaInsets.bottom
        customTabBar.frame = CGRect(
            x: 0,
            y: view.bounds.height - height,
            width: view.bounds.width,
            height: height
        )
        
        // 确保TabBar显示
        customTabBar.isHidden = false
        
        // 打印调试信息
        print("CustomTabBarController viewDidLoad: TabBar frame = \(customTabBar.frame)")
        print("CustomTabBarController viewDidLoad: view bounds = \(view.bounds)")
        print("CustomTabBarController viewDidLoad: safeAreaInsets = \(view.safeAreaInsets)")
        
        // 添加通知观察者，监听设备方向变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(orientationDidChange),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )

        // 监听分享事件
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleShareNotification(_:)),
            name: .shareRequested,
            object: nil
        )
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateTabBarFrame()
        
        // 打印调试信息
        print("CustomTabBarController viewWillAppear: TabBar frame = \(customTabBar.frame)")
        print("CustomTabBarController viewWillAppear: TabBar isHidden = \(customTabBar.isHidden)")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        updateTabBarFrame()
        
        // 打印调试信息
        print("CustomTabBarController viewDidAppear: TabBar frame = \(customTabBar.frame)")
        print("CustomTabBarController viewDidAppear: TabBar isHidden = \(customTabBar.isHidden)")
        
        // 只在首次进入时显示TabBar，后续不再自动强制显示，避免子页面hideTabBar失效
        if isFirstAppearance {
            isFirstAppearance = false
            showTabBar()
            print("CustomTabBarController viewDidAppear: 首次显示TabBar")
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateTabBarFrame()
        
        // 打印调试信息
        print("CustomTabBarController viewDidLayoutSubviews: TabBar frame = \(customTabBar.frame)")
    }
    
    // 监听设备方向变化
    @objc private func orientationDidChange() {
        updateTabBarFrame()
    }
    
    // 更新TabBar的frame
    private func updateTabBarFrame() {
        let height = 44 + view.safeAreaInsets.bottom
        customTabBar.frame = CGRect(
            x: 0,
            y: view.bounds.height - height,
            width: view.bounds.width,
            height: height
        )
        
        // 确保TabBar在最上层
        view.bringSubviewToFront(customTabBar)
        
        // 打印调试信息
        print("CustomTabBarController updateTabBarFrame: TabBar frame = \(customTabBar.frame)")
    }
    
    // 设置视图控制器
    override func setViewControllers(_ viewControllers: [UIViewController]?, animated: Bool) {
        guard let viewControllers = viewControllers else {
            super.setViewControllers(nil, animated: animated)
            return
        }
        
        self.customViewControllers = viewControllers
        super.setViewControllers(viewControllers, animated: animated)
        
        // 确保所有子视图控制器的导航栏都隐藏，但不影响导航控制器功能
        viewControllers.forEach { viewController in
            if let navController = viewController as? UINavigationController {
                navController.setNavigationBarHidden(true, animated: false)
            }
        }
        
        // 配置 TabBar 项
        var tabItems: [TabBarItem] = []
        for (index, controller) in viewControllers.enumerated() {
            let title = controller.title ?? ""
            let isAddButton = index == 2 // 假设第三个是加号按钮
            let item = TabBarItem(title: title, isAddButton: isAddButton)
            tabItems.append(item)
        }
        
        customTabBar.setItems(tabItems)
        
        // 更新TabBar的frame
        updateTabBarFrame()
        
        // 设置默认选中项
        selectTab(at: 0)
        
        // 打印调试信息
        print("CustomTabBarController setViewControllers: TabBar items count = \(tabItems.count)")
    }
    
    // 选择特定的选项卡
    func selectTab(at index: Int) {
        // 如果是中间的发布按钮，弹出视频录制页面
        if index == 2 {
            // 切换回浅色外观，避免停留在深色主题
            customTabBar.updateAppearance(isDark: false)
            //先跳转测试页面
//            let videoRecordVC = VideoSDKTestViewController()
            let videoRecordVC = VideoRecordViewController()
            let nav = HiddenNavController(rootViewController: videoRecordVC)
            nav.modalPresentationStyle = .fullScreen
            present(nav, animated: true)
            return
        }
        
        guard index < customViewControllers.count else { return }
        
        // 更新选中索引
        selectedTabIndex = index
        
        // 更新 TabBar 选中状态
        customTabBar.setSelectedIndex(index)
        
        // 新增：根据所选索引切换 TabBar 颜色外观（index 1 = "朋友" -> 黑底白字，其余恢复白底黑字）
        if index == 1 {
            customTabBar.updateAppearance(isDark: true)
        } else {
            customTabBar.updateAppearance(isDark: false)
        }
        
        // 更新显示的视图控制器
        selectedIndex = index

        // 在成功切换到目标页面后，如目标为“我的”页，则刷新其数据
        refreshMePageIfNeeded(for: index)
        
        // 打印调试信息
        print("CustomTabBarController selectTab: 选中索引 = \(index)")
    }
    
    // MARK: - 切换到“我的”页后的数据刷新
    /// 如果目标索引对应的是 MeViewController，则调用其 `refreshData()` 方法进行数据刷新
    /// - Parameter index: 当前选中的 Tab 索引
    private func refreshMePageIfNeeded(for index: Int) {
        guard index < customViewControllers.count else { return }
        var target = customViewControllers[index]
        // 若为导航控制器，取其可见控制器/top
        if let nav = target as? UINavigationController {
            target = nav.topViewController ?? nav
        }
        if let meVC = target as? MeViewController {
            print("CustomTabBarController: 准备刷新 Me 页数据")
            meVC.refreshData()
        }
    }
    
    // 显示TabBar
    func showTabBar(animated: Bool = false) {
        print("CustomTabBarController showTabBar: 当前状态 = \(customTabBar.isHidden)")
        
        // 如果已经显示，不需要再次处理
        if !customTabBar.isHidden {
            print("CustomTabBarController showTabBar: TabBar已经显示，不需要再次处理")
            return
        }
        
        // 停止所有正在进行的动画
        customTabBar.layer.removeAllAnimations()
        
        // 更新TabBar的frame
        updateTabBarFrame()
        
        if animated {
            // 设置初始位置（在屏幕底部以下）
            customTabBar.frame.origin.y = view.bounds.height
            
            // 显示TabBar
            customTabBar.isHidden = false
            customTabBar.alpha = 1.0
            
            // 执行动画
            UIView.animate(withDuration: 0.25) {
                self.updateTabBarFrame()
            }
        } else {
            // 无动画直接显示
            customTabBar.isHidden = false
            customTabBar.alpha = 1.0
            updateTabBarFrame()
        }
        
        // 确保TabBar在最上层
        view.bringSubviewToFront(customTabBar)
        
        // 强制布局更新
        view.layoutIfNeeded()
        
        // 打印调试信息
//        print("CustomTabBarController showTabBar: TabBar已显示，frame = \(customTabBar.frame)")
    }
    
    // 隐藏TabBar
    func hideTabBar(animated: Bool = true) {
        // 检查当前是否已经隐藏，避免重复操作
        if customTabBar.isHidden {
//            print("CustomTabBarController hideTabBar: TabBar已经隐藏，不需要再次处理")
            return
        }
        
        // 检查当前选中的控制器是否是 HomeViewController 或其子控制器
        if let selectedVC = selectedViewController {
            if selectedVC is HomeViewController ||
               (selectedVC is UINavigationController && (selectedVC as! UINavigationController).topViewController is HomeViewController) {
                print("CustomTabBarController hideTabBar: 当前是 HomeViewController，不隐藏 TabBar")
                return
            }
        }
        
        print("CustomTabBarController hideTabBar: 当前状态 = \(customTabBar.isHidden)")
        
        if animated {
            UIView.animate(withDuration: 0.3) {
                self.customTabBar.alpha = 0
            } completion: { _ in
                self.customTabBar.isHidden = true
            }
        } else {
            customTabBar.isHidden = true
        }
        
//        print("CustomTabBarController hideTabBar: TabBar已隐藏")
    }
    
    // 打印视图层次结构
    private func printViewHierarchy() {
        print("CustomTabBarController 视图层次结构:")
        print("- view: \(view)")
        print("- subviews count: \(view.subviews.count)")
        for (index, subview) in view.subviews.enumerated() {
            print("  - subview[\(index)]: \(subview), isHidden: \(subview.isHidden), alpha: \(subview.alpha), frame: \(subview.frame)")
        }
    }
    
    // 强制保持 TabBar 的显示状态
    func forceKeepTabBarVisible() {
        // 停止所有正在进行的动画
        customTabBar.layer.removeAllAnimations()
        
        // 确保 TabBar 显示
        customTabBar.isHidden = false
        customTabBar.alpha = 1.0
        
        // 更新 TabBar 的 frame
        updateTabBarFrame()
        
        // 确保 TabBar 在最上层
        view.bringSubviewToFront(customTabBar)
        
        // 打印调试信息
        print("CustomTabBarController forceKeepTabBarVisible: TabBar 已强制显示")
    }

    // 启用 TabBar 交互
    func enableInteraction() {
        print("CustomTabBarController: Enabling interaction")
        customTabBar.isUserInteractionEnabled = true
    }

    // 禁用 TabBar 交互
    func disableInteraction() {
        print("CustomTabBarController: Disabling interaction")
        customTabBar.isUserInteractionEnabled = false
    }

    // 新增：弹出升级弹窗的方法（推荐用这个）
    func presentUpgradePopup(with data: AppVersionData) {
        // 避免重复弹窗
        if presentedViewController is UpgradePopupViewController { return }
        let upgradeVC = UpgradePopupViewController()
        upgradeVC.versionData = data
        upgradeVC.modalPresentationStyle = .overFullScreen
        upgradeVC.modalTransitionStyle = .crossDissolve
        self.present(upgradeVC, animated: true, completion: nil)
        print("CustomTabBarController is presenting UpgradePopupViewController with data")
    }

    // 保留原 presentUpgradePopup() 兼容老代码（可选，建议废弃）
    func presentUpgradePopup() {
        print("请使用 presentUpgradePopup(with:) 传递数据")
    }

    // 检查用户是否登录（占位符，需要替换为实际逻辑）
    private func isUserLoggedIn() -> Bool {
        // 使用AuthManager检查登录状态
        let loggedIn = AuthManager.shared.isLoggedIn
        print("检查用户登录状态: \(loggedIn ? "已登录" : "未登录")")
        return loggedIn
    }

    // MARK: - Share Handling
    @objc private func handleShareNotification(_ notif: Notification) {
        print("=== 收到分享通知 ===")
        guard let payload = notif.object as? SharePayload else {
            print("❌ 分享载体为空")
            return
        }

        print("分享类型: \(payload.type)")
        print("分享标题: \(payload.title)")
        print("是否有图片: \(payload.image != nil)")

        let sheet = ShareSheetView()

        // 根据 payload 动态生成分享行为
        sheet.shareToWeChatAction = { [weak self] in
            print("点击微信好友分享")
            self?.shareToWeChat(payload: payload, scene: 0)
        }

        sheet.shareToMomentsAction = { [weak self] in
            print("点击朋友圈分享")
            self?.shareToWeChat(payload: payload, scene: 1)
        }

        sheet.copyLinkAction = { [weak self] in
            if let shareText = payload.extras?["shareText"] as? String, let link = payload.link {
                // 复制完整的分享文案（包含链接）
                let fullText = shareText + "\n" + link
                UIPasteboard.general.string = fullText
                self?.showHighLevelToast("复制成功")
            } else if let shareText = payload.extras?["shareText"] as? String {
                // 只有分享文案，没有链接
                UIPasteboard.general.string = shareText
                self?.showHighLevelToast("复制成功")
            } else if let link = payload.link {
                UIPasteboard.general.string = link
                self?.showHighLevelToast("链接复制成功")
            } else if let command = payload.extras?["command"] as? String {
                UIPasteboard.general.string = command
                self?.showHighLevelToast("链接复制成功，快去分享给好友吧")
            }
        }

        if let window = view.window ?? UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            print("显示分享弹窗")
            sheet.show(in: window)
        } else {
            print("❌ 无法找到合适的窗口显示分享弹窗")
        }
    }

    /// 分享到微信
    private func shareToWeChat(payload: SharePayload, scene: Int32) {
        print("=== 开始微信分享 ===")
        print("分享场景: \(scene == 0 ? "好友" : "朋友圈")")
        print("微信是否安装: \(WXApi.isWXAppInstalled())")
        print("微信是否支持API: \(WXApi.isWXAppSupport())")

        // 检查微信是否可用
        guard WXApi.isWXAppInstalled() else {
            print("❌ 微信未安装")
            showHighLevelToast("请先安装微信")
            return
        }

        guard WXApi.isWXAppSupport() else {
            print("❌ 微信版本不支持")
            showHighLevelToast("微信版本过低，请升级微信")
            return
        }

        if let image = payload.image, let data = image.jpegData(compressionQuality: 0.9) {
            print("✅ 执行图片分享，数据大小: \(data.count) bytes")
            // 图片分享
            shareImageToWeChat(image: image, data: data, scene: scene)
        } else if case .video = payload.type, let link = payload.link {
            print("✅ 执行视频分享")
            // 视频分享 - 分享网页链接并带上封面图
            shareVideoToWeChat(payload: payload, scene: scene)
        } else if let link = payload.link {
            print("✅ 执行链接分享")
            // 纯文本/链接分享
            shareTextToWeChat(text: payload.title + " " + link, scene: scene)
        } else if let command = payload.extras?["command"] as? String {
            print("✅ 执行文本分享（分享口令）")
            // 分享口令文本
            shareTextToWeChat(text: command, scene: scene)
        } else {
            print("❌ 无有效分享内容")
            print("payload.image: \(payload.image != nil)")
            print("payload.link: \(payload.link ?? "nil")")
            print("payload.extras: \(payload.extras ?? [:])")
            showHighLevelToast("分享内容无效")
        }
    }

    /// 分享图片到微信
    private func shareImageToWeChat(image: UIImage, data: Data, scene: Int32) {
        print("=== 分享图片到微信 ===")
        print("图片尺寸: \(image.size)")
        print("数据大小: \(data.count) bytes")
        print("分享场景: \(scene)")

        let imgObj = WXImageObject()
        imgObj.imageData = data
        let message = WXMediaMessage()
        message.mediaObject = imgObj
        message.setThumbImage(image)
        let req = SendMessageToWXReq()
        req.bText = false
        req.message = message
        req.scene = scene

        let success = WXApi.send(req)
        print("微信分享请求发送结果: \(success)")

//        if !success {
//            print("❌ 微信分享请求发送失败")
//            showHighLevelToast("分享失败，请重试")
//        } else {
//            print("✅ 微信分享请求发送成功，等待微信响应")
//        }
    }

    /// 分享文本到微信
    private func shareTextToWeChat(text: String, scene: Int32) {
        print("=== 分享文本到微信 ===")
        print("分享文本: \(text)")
        print("分享场景: \(scene == 0 ? "好友" : "朋友圈")")

        let req = SendMessageToWXReq()
        req.text = text
        req.bText = true
        req.scene = scene

        let success: Void = WXApi.send(req)
        print("微信文本分享请求发送结果: \(success)")

//        if !success {
//            print("❌ 微信文本分享请求发送失败")
//            showHighLevelToast("分享失败，请重试")
//        } else {
//            print("✅ 微信文本分享请求发送成功，等待微信响应")
//        }
    }

    /// 分享视频到微信（网页形式，带封面图）
    private func shareVideoToWeChat(payload: SharePayload, scene: Int32) {
        let webpageObject = WXWebpageObject()
        webpageObject.webpageUrl = payload.link ?? ""

        let message = WXMediaMessage()
        message.mediaObject = webpageObject

        // 从payload中获取视频信息
        if let videoItem = payload.extras?["videoItem"] as? VideoItem {
            // 微信卡片格式：
            // 卡片图片 = 视频封面
            // 卡片标题 = 视频简介
            // 卡片副标题：
            // 作者：作者名称
            // 播放：5026

            message.title = videoItem.worksTitle ?? "精彩视频"

            // 构建副标题
            var descriptionParts: [String] = []
            if let authorName = videoItem.svUserMainVo?.customerName, !authorName.isEmpty {
                descriptionParts.append("作者：\(authorName)")
            }
            if let watchCount = videoItem.watchNumber, watchCount > 0 {
                descriptionParts.append("播放：\(videoItem.formattedWatchCount)")
            }
            message.description = descriptionParts.joined(separator: "\n")
        } else {
            // 兜底处理
            message.title = payload.title
            message.description = payload.description ?? payload.title
        }

        // 如果有封面图URL，使用Kingfisher下载并设置缩略图
        if let thumbnailURL = payload.thumbnail, !thumbnailURL.isEmpty, let url = URL(string: thumbnailURL) {
            print("[VideoShare] 开始下载封面图: \(thumbnailURL)")

            // 使用Kingfisher下载图片
            KingfisherManager.shared.retrieveImage(with: url) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let imageResult):
                        print("[VideoShare] 封面图下载成功")
                        // 压缩图片以符合微信要求（缩略图不能超过32KB）
                        let thumbnailImage = self?.compressImageForWechat(imageResult.image) ?? imageResult.image
                        message.setThumbImage(thumbnailImage)

                        let req = SendMessageToWXReq()
                        req.bText = false
                        req.message = message
                        req.scene = scene
                        let success = WXApi.send(req)
//                        print("[VideoShare] 微信分享请求发送\(success ? "成功" : "失败")")

                    case .failure(let error):
                        print("[VideoShare] 封面图下载失败: \(error.localizedDescription)")
                        // 下载失败时使用默认图片
                        if let defaultImage = UIImage(named: "app_icon") {
                            let thumbnailImage = self?.compressImageForWechat(defaultImage) ?? defaultImage
                            message.setThumbImage(thumbnailImage)
                        }

                        let req = SendMessageToWXReq()
                        req.bText = false
                        req.message = message
                        req.scene = scene
                        let success = WXApi.send(req)
//                        print("[VideoShare] 微信分享请求发送\(success ? "成功" : "失败")（使用默认图片）")
                    }
                }
            }
        } else {
            // 没有封面图时使用默认图片
            print("[VideoShare] 没有封面图，使用默认图片")
            if let defaultImage = UIImage(named: "app_icon") {
                let thumbnailImage = compressImageForWechat(defaultImage)
                message.setThumbImage(thumbnailImage)
            }

            let req = SendMessageToWXReq()
            req.bText = false
            req.message = message
            req.scene = scene
            let success = WXApi.send(req)
//            print("[VideoShare] 微信分享请求发送\(success ? "成功" : "失败")（无封面图）")
        }
    }

    /// 压缩图片以符合微信分享要求
    private func compressImageForWechat(_ image: UIImage) -> UIImage {
        // 微信缩略图要求：不超过32KB，建议尺寸150x150
        let targetSize = CGSize(width: 150, height: 150)

        // 先调整尺寸
        UIGraphicsBeginImageContextWithOptions(targetSize, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: targetSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
        UIGraphicsEndImageContext()

        // 压缩质量，确保不超过32KB
        var compressionQuality: CGFloat = 0.8
        var imageData = resizedImage.jpegData(compressionQuality: compressionQuality)

        while let data = imageData, data.count > 32 * 1024 && compressionQuality > 0.1 {
            compressionQuality -= 0.1
            imageData = resizedImage.jpegData(compressionQuality: compressionQuality)
        }

        if let finalData = imageData, let finalImage = UIImage(data: finalData) {
            print("[VideoShare] 图片压缩完成，大小: \(finalData.count) bytes, 质量: \(compressionQuality)")
            return finalImage
        }

        return resizedImage
    }

    /// 显示高层级Toast（在最顶层窗口显示，不会被其他视图遮挡）
    private func showHighLevelToast(_ message: String, duration: TimeInterval = 2.0) {
        // 延迟一点显示，确保分享面板已经关闭
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 创建容器视图
            let toastContainer = UIView()
            toastContainer.backgroundColor = UIColor.black.withAlphaComponent(0.8)
            toastContainer.layer.cornerRadius = 8
            toastContainer.clipsToBounds = true
            toastContainer.alpha = 0

            // 创建消息标签
            let messageLabel = UILabel()
            messageLabel.text = message
            messageLabel.textColor = .white
            messageLabel.numberOfLines = 0
            messageLabel.textAlignment = .center
            messageLabel.font = .systemFont(ofSize: 14)
            messageLabel.translatesAutoresizingMaskIntoConstraints = false

            // 添加视图层级
            toastContainer.addSubview(messageLabel)
            toastContainer.translatesAutoresizingMaskIntoConstraints = false

            // 获取最顶层窗口
            guard let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
            window.addSubview(toastContainer)

            // 设置约束
            NSLayoutConstraint.activate([
                // 消息标签约束
                messageLabel.topAnchor.constraint(equalTo: toastContainer.topAnchor, constant: 12),
                messageLabel.bottomAnchor.constraint(equalTo: toastContainer.bottomAnchor, constant: -12),
                messageLabel.leadingAnchor.constraint(equalTo: toastContainer.leadingAnchor, constant: 16),
                messageLabel.trailingAnchor.constraint(equalTo: toastContainer.trailingAnchor, constant: -16),

                // 容器视图约束
                toastContainer.centerXAnchor.constraint(equalTo: window.centerXAnchor),
                toastContainer.centerYAnchor.constraint(equalTo: window.centerYAnchor),
                toastContainer.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, multiplier: 0.7),
                toastContainer.leadingAnchor.constraint(greaterThanOrEqualTo: window.leadingAnchor, constant: 50)
            ])

            // 确保Toast在最顶层
            window.bringSubviewToFront(toastContainer)

            // 显示动画
            UIView.animate(withDuration: 0.3, animations: {
                toastContainer.alpha = 1
            }) { _ in
                // 2秒后隐藏
                DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                    UIView.animate(withDuration: 0.3, animations: {
                        toastContainer.alpha = 0
                    }) { _ in
                        toastContainer.removeFromSuperview()
                    }
                }
            }
        }
    }
}

// MARK: - Notification.Name 扩展
extension Notification.Name {
    /// 首页 Tab 被双击
    static let homeTabDoubleTapped = Notification.Name("HomeTabDoubleTappedNotification")
}

// MARK: - CustomTabBarDelegate
extension CustomTabBarController: CustomTabBarDelegate {
    func tabBar(_ tabBar: CustomTabBar, didSelectItemAt index: Int) {
        // 首页 (index 0) 不需要登录检查，直接切换
        if index == 0 {
            // 检测是否为再次点击首页 Tab（用于双击刷新）
            if selectedTabIndex == 0 {
                // 第二次点击，判断时间间隔是否小于 0.4s 认为是双击
                if let lastTap = lastHomeTabTapDate, Date().timeIntervalSince(lastTap) < 0.4 {
                    print("检测到首页 Tab 双击，发送刷新通知")
                    NotificationCenter.default.post(name: .homeTabDoubleTapped, object: nil)
                }
                // 更新最后一次点击时间
                lastHomeTabTapDate = Date()
            } else {
                // 第一次切换到首页，清空记录
                lastHomeTabTapDate = nil
            }
            print("点击了首页，直接切换")
            selectTab(at: index)
            return
        }
        
        // 其他 Tab 需要检查登录状态
        if isUserLoggedIn() {
            print("用户已登录，切换到 Tab \(index)")
            selectTab(at: index)
        } else {
            AuthCoordinator.shared.startLogin(from: self) { _ in
                // 登录结束后保持在首页，不执行切页
            }
        }
    }

    // --- 辅助方法：弹出原生登录页 (用于后端接口失败时) ---
    private func presentNativeLoginFallback(reason: String) {
        // 如果已经登录成功，不再弹出原生登录页
        if hasLoginSucceeded {
            print("已经登录成功，忽略原生登录页弹出请求: \(reason)")
            return
        }
        
        print("Fallback to native login due to: \(reason)")
        DispatchQueue.main.async {
            print("Attempting to dismiss SDK auth page first via cancelLoginVC...")
            // **Step 1: Explicitly dismiss the SDK page and wait for completion**
            TXCommonHandler.sharedInstance().cancelLoginVC(animated: false) { [weak self] in
                 // **Step 2: Inside the completion handler, try to present LoginViewController**
                 guard let self = self else { return }
                 
                 // 再次检查是否已登录成功
                 if self.hasLoginSucceeded {
                    print("在cancelLoginVC回调中检测到已登录成功，不弹出原生登录页")
                    return
                 }
                 
                 print("SDK cancelLoginVC completed for fallback. Proceeding to present native login.")

                 // **Step 3: Add a small delay for stability after dismissal**
                 DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                     // **在此处显示 Toast**
                     self.showFallbackToast("一键登录异常，暂不支持") // 显示提示

                     // 稍作延迟再 present，让 Toast 有机会显示出来
                     DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                         // 再次检查是否已登录成功
                         if self.hasLoginSucceeded {
                            print("Toast显示后检测到已登录成功，不弹出原生登录页")
                            return
                         }
                         
                         if self.isViewLoaded && self.view.window != nil {
                             // Try presenting from self again, it might be valid now
                             print("CustomTabBarController is valid after SDK dismissal, presenting LoginViewController...")
                             let loginVC = LoginViewController2_0()
                             loginVC.modalPresentationStyle = .fullScreen
                             self.present(loginVC, animated: true, completion: {
                                 print("原生 LoginViewController (backend fallback) present 完成")
                             })
                         } else {
                             // If self is still not usable, try rootVC
                             print("CustomTabBarController still not usable after SDK dismissal (\(self.isViewLoaded ? "view loaded" : "view not loaded"), window: \(self.view.window == nil ? "nil" : "exists"))，尝试从 rootViewController 弹出 LoginViewController")
                             if let rootVC = UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.rootViewController {
                                 // Check if rootVC is busy *after* SDK dismissal attempt
                                 if rootVC.presentedViewController == nil {
                                     print("Attempting to present LoginViewController from rootVC: \(rootVC)")
                                     let loginVC = LoginViewController2_0()
                                     loginVC.modalPresentationStyle = .fullScreen
                                     rootVC.present(loginVC, animated: true, completion: {
                                         print("原生 LoginViewController (rootVC fallback) present 完成")
                                     })
                                 } else {
                                     // If rootVC is *still* presenting something else (maybe not the SDK page?)
                                     print("错误：RootViewController (\(rootVC)) is still presenting (\(rootVC.presentedViewController!)) even after SDK dismissal attempt，无法 fallback。")
                                 }
                             } else {
                                 print("错误：无法获取到 rootViewController，无法 fallback。")
                             }
                         }
                     }
                 }
            }
        }
    }
}

// MARK: - 自定义 TabBar
protocol CustomTabBarDelegate: AnyObject {
    func tabBar(_ tabBar: CustomTabBar, didSelectItemAt index: Int)
}

class CustomTabBar: UIView {
    
    // 代理
    weak var delegate: CustomTabBarDelegate?
    
    // TabBar 项
    private var items: [TabBarItem] = []
    
    // 按钮数组
    private var buttons: [UIButton] = []
    
    // 当前选中的索引
    private var selectedIndex: Int = 0
    
    // 是否处于黑色主题 (朋友 Tab 选中时)
    private var isDarkStyle: Bool = false
    
    // 顶部分隔线
    private let separatorLine = UIView()
    
    // 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        // 默认背景色
        backgroundColor = .white
        
        // 添加顶部分隔线
        separatorLine.backgroundColor = UIColor(hex: "#EEEEEE")
        addSubview(separatorLine)
        separatorLine.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            separatorLine.topAnchor.constraint(equalTo: topAnchor),
            separatorLine.leadingAnchor.constraint(equalTo: leadingAnchor),
            separatorLine.trailingAnchor.constraint(equalTo: trailingAnchor),
            separatorLine.heightAnchor.constraint(equalToConstant: 0.5)
        ])
    }
    
    // MARK: - 外观切换
    /// 切换 TabBar 的外观样式
    /// - Parameter isDark: `true` 表示黑底白字，`false` 表示白底黑字
    func updateAppearance(isDark: Bool) {
        // 如果状态未变化，则无需更新
        guard isDarkStyle != isDark else { return }
        isDarkStyle = isDark

        // 更新背景色
        backgroundColor = isDark ? .black : .white

        // 更新分隔线颜色（深色时隐藏分隔线）
        separatorLine.isHidden = isDark

        // 更新按钮文字颜色（非选中状态）
        for (index, button) in buttons.enumerated() {
            // 跳过发布按钮
            guard !items[index].isAddButton else { continue }

            // 如果当前按钮已选中，则颜色保持选中颜色（橙色）
            if index == selectedIndex {
                button.setTitleColor(UIColor(hex: "#FB6C04"), for: .normal)
            } else {
                let normalColor: UIColor = isDark ? .white : UIColor(hex: "#444444")
                button.setTitleColor(normalColor, for: .normal)
            }
        }
    }
    
    // 设置 TabBar 项
    func setItems(_ items: [TabBarItem]) {
        self.items = items
        
        // 清除现有按钮
        buttons.forEach { $0.removeFromSuperview() }
        buttons.removeAll()
        
        // 创建新按钮
        setupButtons()
    }
    
    private func setupButtons() {
        // 清除现有按钮
        buttons.forEach { $0.removeFromSuperview() }
        buttons.removeAll()
        
        // 获取发布按钮的索引
        let addButtonIndex = items.firstIndex { $0.isAddButton } ?? 2
        
        // 计算按钮宽度和间距
        let totalWidth = bounds.width
        let addButtonWidth: CGFloat = 70 // 发布按钮宽度
        let normalButtonCount = CGFloat(items.count - 1) // 除发布按钮外的按钮数量
        let normalButtonWidth = (totalWidth - addButtonWidth) / normalButtonCount
        
        // 创建按钮
        for (index, item) in items.enumerated() {
            let button = UIButton(type: .custom)
            button.tag = index
            
            if item.isAddButton {
                // 发布按钮 - 使用图片
                button.setImage(UIImage(named: "tabbar_add_btn"), for: .normal)
                button.imageView?.contentMode = .scaleAspectFit
                
                // 设置按钮大小和位置 - 居中
                let buttonX = (totalWidth - addButtonWidth) / 2
                let buttonY = 4 // 稍微上移一点，使按钮更突出
                button.frame = CGRect(x: buttonX, y: CGFloat(buttonY), width: addButtonWidth, height: 36)
            } else {
                // 普通按钮 - 使用文本
                button.setTitle(item.title, for: .normal)
                button.setTitleColor(UIColor(hex: "#444444"), for: .normal)
                button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16) // 恢复原始字体大小
                
                // 设置按钮大小和位置
                let buttonX: CGFloat
                
                if index < addButtonIndex {
                    // 发布按钮左侧的按钮
                    buttonX = CGFloat(index) * normalButtonWidth
                } else {
                    // 发布按钮右侧的按钮
                    buttonX = totalWidth - (CGFloat(items.count - index)) * normalButtonWidth
                }
                
                button.frame = CGRect(x: buttonX, y: 0, width: normalButtonWidth, height: 44)
                
                // 确保文字居中
                button.contentHorizontalAlignment = .center
                button.contentVerticalAlignment = .center
                button.titleEdgeInsets = .zero
                
                // 尝试使用 Source Han Sans CN-Bold 字体
                if let customFont = UIFont(name: "Source Han Sans CN-Bold", size: 16) {
                    button.titleLabel?.font = customFont
                }
            }
            
            // 添加点击事件
            button.addTarget(self, action: #selector(buttonTapped(_:)), for: .touchUpInside)
            
            // 添加到视图和数组
            addSubview(button)
            buttons.append(button)
        }
        
        // 更新选中状态
        setSelectedIndex(selectedIndex)
    }
    
    // 按钮点击事件
    @objc private func buttonTapped(_ sender: UIButton) {
        let index = sender.tag
        print("TabBar按钮被点击: \(index)")
        delegate?.tabBar(self, didSelectItemAt: index)
    }
    
    // 设置选中索引
    func setSelectedIndex(_ index: Int) {
        guard index < buttons.count else { return }
        
        // 如果是发布按钮，不进行选中状态处理
        if index == 2 {
            return
        }
        
        selectedIndex = index
        
        for (i, button) in buttons.enumerated() {
            if i == index && !items[i].isAddButton {
                // 选中状态 - 应用固定主题色 #FB6602
                removeGradientFromButton(button)
                button.setTitleColor(UIColor(hex: "#FB6602"), for: .normal)
            } else if !items[i].isAddButton {
                // 未选中状态 - 根据当前主题切换颜色
                removeGradientFromButton(button)
                let normalColor: UIColor = isDarkStyle ? .white : UIColor(hex: "#444444")
                button.setTitleColor(normalColor, for: .normal)
            }
        }
    }
    
    // 应用渐变色到按钮（已注释，不再使用渐变色）
    private func applyGradientToButton(_ button: UIButton) {
        // 原渐变色相关代码已注释
        /*
        // 移除现有渐变层
        removeGradientFromButton(button)
        
        // 确保按钮标题标签已经布局
        button.layoutIfNeeded()
        
        guard let titleLabel = button.titleLabel,
              let text = titleLabel.text,
              !text.isEmpty else { return }
        
        // 计算文本大小
        let textSize = (text as NSString).size(withAttributes: [
            .font: titleLabel.font ?? UIFont.boldSystemFont(ofSize: 16)
        ])
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(red: 1, green: 0.55, blue: 0.21, alpha: 1).cgColor,  // #FF8D36
            UIColor(red: 1, green: 0.35, blue: 0.35, alpha: 1).cgColor   // #FF5858
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        
        // 使用计算出的文本大小，并确保居中
        let x = (titleLabel.bounds.width - textSize.width) / 2
        let y = (titleLabel.bounds.height - textSize.height) / 2
        gradientLayer.frame = CGRect(x: x, y: y, width: textSize.width, height: textSize.height)
        
        // 创建文本遮罩
        let textLayer = CATextLayer()
        textLayer.string = text
        textLayer.font = titleLabel.font
        textLayer.fontSize = titleLabel.font?.pointSize ?? 16
        textLayer.alignmentMode = .center
        textLayer.frame = CGRect(origin: .zero, size: textSize)
        
        // 应用文本遮罩到渐变层
        gradientLayer.mask = textLayer
        
        // 隐藏原始文本
        button.setTitleColor(.clear, for: .normal)
        
        // 添加渐变层到按钮
        titleLabel.layer.addSublayer(gradientLayer)
        */
    }
    
    // 移除按钮的渐变色（保留此方法以确保清除任何现有渐变层）
    private func removeGradientFromButton(_ button: UIButton) {
        button.titleLabel?.layer.sublayers?.forEach { layer in
            if layer is CAGradientLayer {
                layer.removeFromSuperlayer()
            }
        }
    }
}

// MARK: - TabBar 项模型
struct TabBarItem {
    let title: String
    let isAddButton: Bool
}

// MARK: - Toast Helper
extension CustomTabBarController {
    func showFallbackToast(_ message: String, duration: TimeInterval = 2.0) {
        // **修改点 1: 创建容器 UIView**
        let toastContainer = UIView()
        toastContainer.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        toastContainer.layer.cornerRadius = 10
        toastContainer.clipsToBounds = true
        toastContainer.alpha = 0

        // 创建 Label
        let toastLabel = UILabel()
        // **修改点 2: 移除 Label 的背景和圆角**
        // toastLabel.backgroundColor = ...
        toastLabel.textColor = .white
        toastLabel.textAlignment = .center
        toastLabel.font = UIFont.systemFont(ofSize: 14)
        toastLabel.text = message
        // toastLabel.layer.cornerRadius = ...
        // toastLabel.clipsToBounds = ...
        toastLabel.numberOfLines = 0

        // **修改点 3: 将 Label 添加到容器中**
        toastContainer.addSubview(toastLabel)
        toastLabel.translatesAutoresizingMaskIntoConstraints = false

        // **修改点 4: 设置 Label 在容器内的约束 (增加内边距)**
        let horizontalPadding: CGFloat = 15
        let verticalPadding: CGFloat = 8
        NSLayoutConstraint.activate([
            toastLabel.leadingAnchor.constraint(equalTo: toastContainer.leadingAnchor, constant: horizontalPadding),
            toastLabel.trailingAnchor.constraint(equalTo: toastContainer.trailingAnchor, constant: -horizontalPadding),
            toastLabel.topAnchor.constraint(equalTo: toastContainer.topAnchor, constant: verticalPadding),
            toastLabel.bottomAnchor.constraint(equalTo: toastContainer.bottomAnchor, constant: -verticalPadding)
        ])

        // 添加到 keyWindow
        guard let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
        // **修改点 5: 将容器添加到 window**
        window.addSubview(toastContainer)
        toastContainer.translatesAutoresizingMaskIntoConstraints = false

        // **修改点 6: 设置容器的约束**
        NSLayoutConstraint.activate([
            toastContainer.centerXAnchor.constraint(equalTo: window.centerXAnchor),
            toastContainer.centerYAnchor.constraint(equalTo: window.centerYAnchor),
            // 容器宽度不超过屏幕 80%
            toastContainer.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, multiplier: 0.8)
            // 容器高度由内部标签和内边距决定，宽度也应自适应内容，但有最大限制
        ])

        // **修改点 7: 动画应用于容器**
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseIn, animations: {
            toastContainer.alpha = 1.0
        }) { _ in
            UIView.animate(withDuration: 0.3, delay: duration, options: .curveEaseOut, animations: {
                toastContainer.alpha = 0.0
            }) { _ in
                toastContainer.removeFromSuperview()
            }
        }
    }
}
